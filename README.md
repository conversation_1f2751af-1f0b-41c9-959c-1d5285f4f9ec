i want to develop a website for "Creative Hydraulics" it should be for both desktop and mobile view.
the 


1. develop a index.html page for "Creative Hydraulics"  where the page should contain 
        1.navbar ---> in navbar it should have 
                        Home
                        About Us
                        Certifications
                        Products
                        Infrastructure
                        Contact Us

        2. Home page ---> in home page it should have
                        1. header section ---> in header section it should have
                        2. about us section ---> in about us section it should have
                        3. certifications section ---> in certifications section it should have
                        4. products section ---> in products section it should have
                        5. infrastructure section ---> in infrastructure section it should have
                        6. contact us section ---> in contact us section it.
        3.footer ---> develop a poper foressinal footer.




                

     