/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ffffff;    /* White */
    --secondary-color: #1e3a8a;  /* Navy Blue */
    --tertiary-color: #dc2626;   /* Red */
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--primary-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation Styles */
.navbar {
    background-color: var(--secondary-color);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--shadow);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--tertiary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 3px 0;
    transition: 0.3s;
}

/* Header Section */
.header-section {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2563eb 100%);
    color: var(--primary-color);
    padding: 120px 0 80px;
    text-align: center;
}

.header-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.header-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #e5e7eb;
}

.header-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: #d1d5db;
}

.header-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background-color: var(--tertiary-color);
    color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #b91c1c;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
}

/* About Section */
.about-section {
    background-color: #f9fafb;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.about-text p {
    margin-bottom: 2rem;
    color: var(--text-light);
    line-height: 1.8;
}

.features-list {
    list-style: none;
}

.features-list li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.features-list i {
    color: var(--tertiary-color);
    margin-right: 0.5rem;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background-color: var(--primary-color);
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.stat-item h4 {
    font-size: 2.5rem;
    color: var(--tertiary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Certifications Section */
.certifications-section {
    background-color: var(--primary-color);
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cert-item {
    text-align: center;
    padding: 2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.cert-item:hover {
    border-color: var(--tertiary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.cert-item i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.cert-item h3 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.cert-item p {
    color: var(--text-light);
}

/* Products Section */
.products-section {
    background-color: #f9fafb;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.product-item {
    background-color: var(--primary-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.3s ease;
}

.product-item:hover {
    transform: translateY(-5px);
}

.product-item i {
    font-size: 3rem;
    color: var(--tertiary-color);
    margin-bottom: 1rem;
}

.product-item h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.product-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Infrastructure Section */
.infrastructure-section {
    background-color: var(--primary-color);
}

.infrastructure-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.infra-item {
    padding: 2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
}

.infra-item:hover {
    border-color: var(--secondary-color);
    box-shadow: var(--shadow);
}

.infra-item i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.infra-item h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.infra-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Contact Section */
.contact-section {
    background-color: #f9fafb;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: grid;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--tertiary-color);
    margin-top: 0.2rem;
}

.contact-item h3 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-form {
    background-color: var(--primary-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
}

/* Footer */
.footer {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-section p,
.footer-section li {
    color: #d1d5db;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--tertiary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--tertiary-color);
    color: var(--primary-color);
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.social-links a:hover {
    background-color: #b91c1c;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--secondary-color);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .header-content h1 {
        font-size: 2.5rem;
    }

    .header-subtitle {
        font-size: 1.2rem;
    }

    .header-buttons {
        flex-direction: column;
        align-items: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        flex-direction: row;
        justify-content: space-around;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .certifications-grid,
    .products-grid,
    .infrastructure-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .about-stats {
        flex-direction: column;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
